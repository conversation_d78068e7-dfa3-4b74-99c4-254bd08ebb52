/* =====================================================
   用戶認證頁面樣式 - auth.css
   ===================================================== */

/* 基礎樣式 */
:root {
  --color-1: rgb(0, 200, 255);
  --color-2: rgb(255, 200, 0);
  --color-3: rgb(47, 255, 92);
  --color-4: rgb(255, 49, 49);
  --color-5: rgb(0, 255, 234);
  --border-color-1: rgb(0, 162, 255);
  --border-color-2: rgb(255, 180, 19);
  --border-color-3: rgb(30, 255, 0);
  --border-color-4: rgb(255, 63, 63);
  --border-color-5: rgb(12, 255, 243);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 128);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
  --bg-gradient: linear-gradient(135deg, var(--color-1) 0%, var(--color-5) 100%);
  --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.auth-body {
  background: var(--bg-gradient);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 20px;
}

/* 布局樣式 */
.auth-container {
  width: 100%;
  max-width: 550px;
  margin: 0 auto;
}

.auth-card {
  background: var(--text-color-2);
  border-radius: 20px;
  box-shadow: var(--card-shadow);
  padding: 40px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.auth-card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-5px);
}

.auth-card-wide {
  max-width: 1500px;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-logo {
  margin-bottom: 20px;
}

.auth-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
}

.auth-title {
  color: var(--color-1);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  color: var(--text-color-1);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.auth-description {
  color: #666;
  font-size: 0.95rem;
  margin: 15px 0 0 0;
  line-height: 1.5;
}

/* 表單樣式 */
.auth-form {
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  color: var(--text-color-1);
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

.form-control {
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: var(--color-1);
  box-shadow: 0 0 0 0.2rem rgba(0, 200, 255, 0.25);
  background-color: var(--text-color-2);
  outline: none;
}

.form-control:hover {
  border-color: var(--border-color-1);
}

.form-check {
  margin: 20px 0;
}

.form-check-input {
  margin-right: 8px;
}

.form-check-label {
  color: var(--text-color-1);
  font-size: 0.95rem;
}

/* 交互樣式 */
.auth-btn {
  width: 100%;
  padding: 14px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
  transition: all 0.3s ease;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 200, 255, 0.3);
  background: linear-gradient(45deg, var(--color-5), var(--color-1));
}

.auth-btn:active {
  transform: translateY(0);
}

.auth-links {
  text-align: center;
  margin-bottom: 25px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.auth-link {
  color: var(--color-1);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: var(--color-5);
  text-decoration: underline;
}

.register-btn {
  background: linear-gradient(45deg, var(--color-3), var(--color-2));
  color: var(--text-color-2);
  border: 2px solid var(--border-color-3);
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(47, 255, 92, 0.3);
}

.register-btn:hover {
  background: linear-gradient(45deg, var(--color-2), var(--color-3));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(47, 255, 92, 0.4);
  color: var(--text-color-2);
  text-decoration: none;
}

/* 語言切換器 */
.language-switcher {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.lang-btn {
  background: transparent;
  border: 1px solid var(--border-color-1);
  border-radius: 8px;
  padding: 8px 12px;
  color: var(--color-1);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.lang-btn:hover {
  background: var(--color-1);
  color: var(--text-color-2);
}

.lang-btn.active {
  background: var(--color-1);
  color: var(--text-color-2);
}

.flag-icon {
  font-size: 1rem;
}

/* 警告和成功消息 */
.alert {
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  border: none;
}

.alert-success {
  background-color: rgba(47, 255, 92, 0.1);
  color: #155724;
  border-left: 4px solid var(--color-3);
}

.alert-danger {
  background-color: rgba(255, 49, 49, 0.1);
  color: #721c24;
  border-left: 4px solid var(--color-4);
}

.alert-link {
  color: inherit;
  font-weight: 600;
  text-decoration: underline;
}

.alert-link:hover {
  text-decoration: none;
}

/* 響應式樣式 */
@media (max-width: 768px) {
  .auth-container {
    max-width: 100%;
    padding: 0 15px;
  }
  
  .auth-card {
    padding: 30px 25px;
    margin: 10px 0;
  }
  
  .auth-card-wide {
    max-width: 100%;
  }
  
  .auth-title {
    font-size: 1.5rem;
  }
  
  .auth-subtitle {
    font-size: 1.3rem;
  }
  
  .language-switcher {
    flex-direction: column;
    align-items: center;
  }
  
  .lang-btn {
    width: 120px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .auth-body {
    padding: 10px;
  }
  
  .auth-card {
    padding: 25px 20px;
  }
  
  .auth-icon {
    font-size: 2.5rem;
  }
  
  .auth-title {
    font-size: 1.3rem;
  }
  
  .auth-subtitle {
    font-size: 1.2rem;
  }
}

/* 動畫效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 載入動畫 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-color-2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 密碼強度驗證樣式 */
.password-requirements {
  margin-top: 10px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--color-2);
}

.password-rules {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
}

.rule-item {
  display: flex;
  align-items: center;
  margin: 5px 0;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.rule-icon {
  margin-right: 8px;
  font-weight: bold;
  width: 16px;
  text-align: center;
}

.rule-item.valid {
  color: var(--color-3);
}

.rule-item.valid .rule-icon {
  color: var(--color-3);
}

.rule-item.invalid {
  color: var(--color-4);
}

.rule-item.invalid .rule-icon {
  color: var(--color-4);
}

/* 密碼匹配提示 */
.password-match {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.password-match.match {
  background-color: rgba(47, 255, 92, 0.1);
  border-left: 4px solid var(--color-3);
}

.password-match.no-match {
  background-color: rgba(255, 49, 49, 0.1);
  border-left: 4px solid var(--color-4);
}

.match-message {
  font-size: 0.9rem;
  font-weight: 500;
}

.password-match.match .match-message {
  color: var(--color-3);
}

.password-match.no-match .match-message {
  color: var(--color-4);
}