<?php
session_start();

// 如果已經登入，重定向到主頁
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

// 處理登入表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'php/DatabaseMySQLi.php';
    
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = '請輸入用戶名和密碼';
    } else {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // 查詢用戶
            $stmt = $conn->prepare("SELECT id, username, password, user_type, status, login_attempts, locked_until, email_verified FROM users WHERE username = ? OR email = ?");
            $stmt->bind_param('ss', $username, $username);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($user = $result->fetch_assoc()) {
                // 檢查帳號狀態
                if ($user['status'] !== 'active') {
                    if ($user['status'] === 'inactive' && !$user['email_verified']) {
                        $error = '請先驗證您的郵箱地址。請檢查您的郵件並點擊驗證鏈接。';
                    } else {
                        $error = '帳號未激活或已被禁用';
                    }
                } elseif (!$user['email_verified']) {
                    $error = '請先驗證您的郵箱地址才能登入。請檢查您的郵件並點擊驗證鏈接。';
                } elseif ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                    $error = '帳號已被鎖定，請稍後再試';
                } elseif (password_verify($password, $user['password'])) {
                    // 登入成功
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['user_type'] = $user['user_type'];
                    
                    // 重置登入嘗試次數
                    $stmt = $conn->prepare("UPDATE users SET login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?");
                    $stmt->bind_param('i', $user['id']);
                    $stmt->execute();
                    
                    // 記住我功能
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
                        // 這裡可以將token存儲到資料庫
                    }
                    
                    header('Location: index.php');
                    exit();
                } else {
                    // 登入失敗，增加嘗試次數
                    $attempts = $user['login_attempts'] + 1;
                    $locked_until = null;
                    
                    if ($attempts >= 3) {
                        $locked_until = date('Y-m-d H:i:s', time() + 900); // 鎖定15分鐘
                    }
                    
                    $stmt = $conn->prepare("UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?");
                    $stmt->bind_param('isi', $attempts, $locked_until, $user['id']);
                    $stmt->execute();
                    
                    $error = '用戶名或密碼錯誤';
                }
            } else {
                $error = '用戶名或密碼錯誤';
            }
        } catch (Exception $e) {
            $error = '系統錯誤，請稍後再試';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入 - KMS Receipt Maker</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/auth.css" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <span class="auth-icon">🧾</span>
                    <h1 class="auth-title">KMS Receipt Maker</h1>
                </div>
                <h2 class="auth-subtitle" data-lang="login_title">User Login</h2>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="auth-form">
                <div class="form-group">
                    <label for="username" class="form-label" data-lang="username">Username/Email</label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label" data-lang="password">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember" data-lang="remember_me">Remember Me</label>
                </div>
                
                <button type="submit" class="btn btn-primary auth-btn" data-lang="login_btn">Login</button>
            </form>
            
            <div class="auth-links">
                <a href="register.php" class="btn btn-outline-primary register-btn" data-lang="register_btn">Register</a>
                <a href="forgot-password.php" class="auth-link" data-lang="forgot_password">Forgot Password?</a>
            </div>
            
            <div class="language-switcher">
                <button class="kms-lang-btn" onclick="changeLanguage('en')" data-lang-code="en">
                    <span class="flag-icon">🌐</span> English
                </button>
                <button class="kms-lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">
                    <span class="flag-icon">🌐</span> 中文
                </button>
            </div>
        </div>
    </div>
    
    <script src="js/language.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>