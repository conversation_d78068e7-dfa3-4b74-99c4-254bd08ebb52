/* 基礎樣式 */
:root {
  --color-1: rgb(0, 200, 255);
  --color-2: rgb(255, 200, 0);
  --color-3: rgb(47, 255, 92);
  --color-4: rgb(255, 49, 49);
  --color-5: rgb(0, 255, 234);
  --border-color-1: rgb(0, 162, 255);
  --border-color-2: rgb(255, 180, 19);
  --border-color-3: rgb(30, 255, 0);
  --border-color-4: rgb(255, 63, 63);
  --border-color-5: rgb(12, 255, 243);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 128);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
}

/* 布局樣式 */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.custom-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.custom-modal {
  background: linear-gradient(135deg, var(--color-1), var(--color-5));
  border-radius: 20px;
  padding: 30px;
  max-width: 450px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: scale(0.7) translateY(-50px);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 2px solid var(--border-color-1);
}

.custom-modal-overlay.show .custom-modal {
  transform: scale(1) translateY(0);
}

.custom-modal-header {
  text-align: center;
  margin-bottom: 20px;
}

.custom-modal-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 15px;
  background: var(--color-3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: var(--text-color-2);
  box-shadow: 0 5px 15px rgba(47, 255, 92, 0.3);
}

.custom-modal-title {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color-2);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.custom-modal-content {
  text-align: center;
  margin-bottom: 30px;
}

.custom-modal-message {
  font-size: 16px;
  color: var(--text-color-2);
  line-height: 1.5;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.custom-modal-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

/* 交互樣式 */
.custom-modal-btn {
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.custom-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.custom-modal-btn:hover::before {
  left: 100%;
}

.custom-modal-btn-confirm {
  background: linear-gradient(135deg, var(--color-3), var(--border-color-3));
  color: var(--text-color-2);
  box-shadow: 0 5px 15px rgba(47, 255, 92, 0.4);
}

.custom-modal-btn-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(47, 255, 92, 0.6);
}

.custom-modal-btn-cancel {
  background: linear-gradient(135deg, var(--color-4), var(--border-color-4));
  color: var(--text-color-2);
  box-shadow: 0 5px 15px rgba(255, 49, 49, 0.4);
}

.custom-modal-btn-cancel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 49, 49, 0.6);
}

.custom-modal-btn:active {
  transform: translateY(0);
}

/* 響應式樣式 */
@media (max-width: 768px) {
  .custom-modal {
    margin: 20px;
    padding: 25px;
  }
  
  .custom-modal-title {
    font-size: 20px;
  }
  
  .custom-modal-message {
    font-size: 14px;
  }
  
  .custom-modal-actions {
    flex-direction: column;
  }
  
  .custom-modal-btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .custom-modal {
    padding: 20px;
  }
  
  .custom-modal-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
  
  .custom-modal-title {
    font-size: 18px;
  }
  
  .custom-modal-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* 彈窗遮罩層 */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--modal-overlay-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--animation-duration) cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 彈窗主體 */
.custom-modal {
  background: var(--modal-bg);
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-shadow);
  min-width: 400px;
  max-width: 500px;
  width: 90%;
  color: var(--modal-text-color);
  overflow: hidden;
  transform: scale(0.7) translateY(-50px);
  transition: all var(--animation-duration) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.custom-modal-overlay.show .custom-modal {
  transform: scale(1) translateY(0);
}

/* 彈窗頭部 */
.custom-modal-header {
  padding: 25px 30px 20px;
  background: var(--modal-header-bg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.custom-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
}

.custom-modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

/* 彈窗內容 */
.custom-modal-body {
  padding: 30px;
  text-align: center;
}

.custom-modal-message {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.95;
  font-weight: 400;
}

/* 彈窗底部 */
.custom-modal-footer {
  padding: 20px 30px 30px;
  background: var(--modal-footer-bg);
  display: flex;
  justify-content: center;
  gap: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 自定義按鈕 */
.custom-btn {
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--btn-shadow);
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.custom-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.custom-btn:hover::before {
  left: 100%;
}

.custom-btn-primary {
  background: var(--btn-primary-bg);
}

.custom-btn-primary:hover {
  transform: var(--btn-hover-transform);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.custom-btn-secondary {
  background: var(--btn-secondary-bg);
}

.custom-btn-secondary:hover {
  transform: var(--btn-hover-transform);
  box-shadow: 0 12px 35px rgba(116, 185, 255, 0.4);
}

.custom-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .custom-modal {
    min-width: 320px;
    margin: 20px;
  }
  
  .custom-modal-header {
    padding: 20px 25px 15px;
  }
  
  .custom-modal-title {
    font-size: 1.3rem;
  }
  
  .custom-modal-body {
    padding: 25px 20px;
  }
  
  .custom-modal-message {
    font-size: 1rem;
  }
  
  .custom-modal-footer {
    padding: 15px 20px 25px;
    flex-direction: column;
    gap: 10px;
  }
  
  .custom-btn {
    width: 100%;
    padding: 14px 20px;
  }

  .credits-stats-grid {
    grid-template-columns: 1fr;
  }

  .credits-stat-card {
    padding: 15px;
  }
}

/* 點數記錄彈窗樣式 */
.credits-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.credits-stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.credits-stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.credits-stat-card .stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.credits-stat-card .stat-info h4 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: var(--modal-text-color);
}

.credits-stat-card .stat-info p {
  font-size: 0.9rem;
  margin: 0.25rem 0 0 0;
  opacity: 0.8;
  color: var(--modal-text-color);
}

.credits-history-list {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
}

.credits-history-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.credits-history-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.credits-history-item.add {
  border-left-color: #4CAF50;
}

.credits-history-item.deduct {
  border-left-color: #f44336;
}

.credits-history-item .item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.credits-history-item .item-type {
  font-weight: 600;
  font-size: 0.9rem;
}

.credits-history-item .item-amount {
  font-weight: 700;
  font-size: 1.1rem;
}

.credits-history-item .item-amount.positive {
  color: #4CAF50;
}

.credits-history-item .item-amount.negative {
  color: #f44336;
}

.credits-history-item .item-description {
  font-size: 0.85rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.credits-history-item .item-date {
  font-size: 0.75rem;
  opacity: 0.6;
}

.loading-indicator {
  text-align: center;
  padding: 2rem;
}

.loading-indicator .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--modal-text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--modal-text-color);
}

.empty-state p {
  opacity: 0.7;
  color: var(--modal-text-color);
}

/* 動畫效果 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.7) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.7) translateY(-50px);
  }
}

/* 深色主題支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --modal-overlay-bg: rgba(0, 0, 0, 0.8);
    --modal-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}

/* 高對比度支持 */
@media (prefers-contrast: high) {
  .custom-modal {
    border: 2px solid #ffffff;
  }
  
  .custom-btn {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}