/**
 * 語言管理系統
 * 支持中文和英文的動態切換
 */

// 語言包定義
const languages = {
    zh: {
        // 基本界面
        app_title: 'KMS 收據生成器',
        nav_create: '創建收據',
        nav_history: '歷史記錄',
        language: '語言',
        user_profile: 'User Profile',
        
        // 收據創建
        create_receipt: '創建新收據',
        customer_info: '客戶信息',
        customer_name: '客戶姓名',
        customer_phone: '聯絡電話',
        customer_email: '電子郵件',
        customer_address: '地址',
        payment_method: '付款方式',
        payment_cash: '現金',
        payment_card: '信用卡',
        payment_transfer: '轉帳',
        payment_other: '其他',
        
        // 項目管理
        receipt_items: '收據項目',
        add_item: '添加項目',
        item_name: '項目名稱',
        item_description: '項目描述',
        item_category: '分類',
        quantity: '數量',
        unit_price: '單價',
        total_price: '總價',
        remove_item: '移除項目',
        select_preset: '選擇預設',
        confirm_add: '確認添加',
        clear: '清空',
        no_items: '尚未添加任何項目',
        receipt_info: '收據信息',
        service: '服務',
        
        // 總計
        totals: '總計',
        subtotal: '小計',
        discount: '折扣金額',
        tax: '稅費',
        total: '總計',
        notes: '備註',
        
        // 操作按鈕
        generate_receipt: '生成收據',
        clear_form: '清空表單',
        save_receipt: '保存收據',
        print_receipt: '打印收據',
        close: '關閉',
        
        // 預覽和歷史
        receipt_preview: '收據預覽',
        preview_placeholder: '收據預覽將在這裡顯示',
        receipt_history: '收據歷史',
        receipt_details: '收據詳情',
        search_placeholder: '搜索收據...',
        
        // 收據字段
        receipt_number: '收據編號',
        receipt_date: '開立日期',
        
        // 表格標題
        item: '項目',
        description: '描述',
        category: '分類',
        qty: '數量',
        price: '單價',
        amount: '金額',
        
        // 狀態
        paid: '已付款',
        pending: '待付款',
        cancelled: '已取消',
        thank_you: '感謝您的惠顧！',
        seller_signature: '賣方簽名',
        buyer_signature: '買方簽名',
        signature_date: '簽名日期',
        
        // 錯誤和成功消息
        error_customer_name: '請輸入客戶姓名',
        error_no_items: '請至少添加一個項目',
        error_invalid_quantity: '請輸入有效的數量',
        error_invalid_price: '請輸入有效的價格',
        success_receipt_saved: '收據保存成功',
        success_receipt_generated: '收據生成成功',
        success_item_added: '項目添加成功',
        success_item_updated: '項目更新成功',
        success_item_removed: '項目已刪除',
        edit_item: '編輯項目',
        save_changes: '保存更改',
        enter_config_name: '請輸入配置名稱',
        config_saved_success: '配置保存成功！',
        config_loaded_success: '配置載入成功！',
        config_deleted_success: '配置刪除成功！',
        config_not_found: '找不到指定的配置',
        confirm_delete_config: '確定要刪除此配置嗎？',
        save_configuration: '保存配置',
        load_configuration: '載入配置',
        manage_configurations: '管理配置',
        add_preset: '新增預設',
        edit_preset: '編輯預設',
        delete_preset: '刪除預設',
        save: '保存',
        cancel: '取消',
        move_up: '上移',
        move_down: '下移',
        confirm_delete_preset: '確定要刪除此預設項目嗎？',
        preset_saved_success: '預設項目保存成功！',
        preset_deleted_success: '預設項目刪除成功！',
        original_price: '原價',
        special_price: '特價',
        discount_percentage: '折扣百分比',
        hide_price: '隱藏價格 (收據上顯示 N/A)',
        update: '更新',
        add_item_description: '點擊下方按鈕添加項目到收據中',
        upload_logo: '上傳 Logo',
        logo_upload_hint: '支持 JPG, PNG, GIF 格式，無檔案大小限制',
        error_invalid_image: '請選擇有效的圖片檔案',
        logo_uploaded_success: 'Logo 上傳成功！',
        logo_preview: 'Logo 預覽',
        logo_info: '圖片資訊',
        file_name: '檔案名稱',
        file_type: '檔案類型',
        file_size: '檔案大小',
        image_dimensions: '圖片尺寸',
        remove_logo: '移除 Logo',
        logo_removed: 'Logo 已移除',
        tax_rate: '稅率',
        clear_all: '清空全部',
        confirm_clear_all: '確定要清空所有項目嗎？',
        all_items_cleared: '所有項目已清空',
        
        // 點數系統
        generating_pdf: '正在生成PDF，請稍候...',
        pdf_generated_success: 'PDF已成功生成並下載',
        pdf_generation_failed: 'PDF生成失敗',
        credits_deducted: '點數已扣除',
        remaining_credits: '剩餘點數',
        credits_warning: '警告: 您的點數不足10點，請聯繫管理員充值',
        print_authorization_success: '打印授權成功！',
        print_authorization_failed: '打印授權失敗，請稍後再試',
        saving: '保存中...',
        save_failed: '保存失敗',
        config_save_failed: '配置保存失敗',
        receipt_saved_pdf_failed: '收據已保存，但PDF生成失敗',
        config_load_failed: '載入配置失敗',
        config_delete_failed: '配置刪除失敗',
        print_failed: '打印失敗',
        delete_failed: '刪除失敗',
        customer_info_updated_success: '客戶信息更新成功',
        customer_info_update_failed: '更新客戶信息失敗',
        credits_deducted_balance: '已扣除 {deducted} 點數，餘額: {balance}',
        subtotal_label: '小計',
        discount_label: '折扣',
        tax_label: '稅額',
        total_label: '總計',
        save_receipt_button: '保存收據',
        receipt_saved_pdf_success: '收據已保存並生成PDF文件',
        save_error: '保存時發生錯誤',
        processing: '處理中...',
        clear_form_title: '清空表單',
        operation_success: '操作成功',
        generate_receipt_first: '請先生成收據',
        notice: '提示',
        confirm_approve_user: '確定要批准這個用戶嗎？',
        confirm_reject_user: '確定要拒絕這個用戶嗎？',
        confirm_delete_user: '確定要刪除這個用戶嗎？此操作無法撤銷！',
        select_users_to_approve: '請選擇要批准的用戶',
        select_users_to_reject: '請選擇要拒絕的用戶',
        confirm_clear_form: '確定要清空表單嗎？這將移除所有已輸入的資料。',
        form_cleared: '表單已清空',
        // Profile page translations
        profile_settings: '個人資料設定',
        back_to_home: '返回首頁',
        logout: '登出',
        delete_account: '刪除帳號',
        delete_account_title: '刪除帳號',
        delete_account_subtitle: '此操作無法撤銷',
        warning_title: '警告',
        warning_permanent: '此操作是永久性的，無法撤銷',
        warning_receipts: '您的所有收據將被永久刪除',
        warning_presets: '您的所有預設項目和配置將丟失',
        warning_credits: '所有剩餘點數將被沒收',
        warning_access: '您將立即失去帳號存取權限',
        your_data_title: '您的資料摘要',
        receipts: '收據',
        presets: '預設項目',
        configurations: '配置',
        transactions: '交易記錄',
        confirmation_required: '需要確認',
        enter_password: '請輸入密碼確認：',
        type_delete: '請輸入 "DELETE" 確認：',
        cancel: '取消',
        registration_date: '註冊日期',
        last_login: '最後登入',
        never_logged_in: '從未登入',
        profile_information: '個人資料',
        members_readonly_notice: '會員只能修改密碼。如需更改其他資訊，請聯絡管理員。',
        username: '用戶名',
        email_address: '電子郵件地址',
        first_name: '名字',
        last_name: '姓氏',
        phone_number: '電話號碼',
        update_profile: '更新個人資料',
        change_password: '修改密碼',
        current_password: '目前密碼',
        please_enter_password: '請輸入密碼',
        new_password: '新密碼',
        password_min_length: '密碼至少需要6個字元',
        confirm_new_password: '確認新密碼',
        // Error messages
        please_enter_current_password: '請輸入目前密碼',
        current_password_incorrect: '目前密碼不正確',
        please_enter_new_password: '請輸入新密碼',
        new_password_min_length: '新密碼至少需要6個字元',
        passwords_do_not_match: '新密碼不一致',
        password_updated_successfully: '密碼更新成功！',
        failed_to_update_password: '密碼更新失敗，請重試',
        system_error_try_later: '系統錯誤，請稍後再試',
        admin: '管理員',
        member: '會員',
        credits_history: '點數使用記錄',
        current_credits: '當前點數',
        total_earned: '總獲得',
        total_spent: '總消費',
        all_types: '所有類型',
        credits_added: '點數增加',
        credits_deducted: '點數扣除',
        all_time: '所有時間',
        today: '今天',
        this_week: '本週',
        this_month: '本月',
        loading: '載入中...',
        no_records: '暫無記錄',
        no_records_desc: '您還沒有任何點數使用記錄',

        // 試用系統
        trial_period: '試用期',
        trial_expires_in: '試用期剩餘',
        trial_expired: '試用期已過期',
        trial_days_remaining: '天',
        trial_upgrade_required: '需要升級',
        trial_feature_restricted: '此功能在試用期間不可用',
        trial_contact_admin: '請聯絡管理員升級為付費會員',
        trial_save_restricted: '試用期間無法使用保存收據功能',
        trial_print_restricted: '試用期間無法使用列印收據功能',
        trial_upgrade_message: '升級為付費會員以解鎖所有功能',

        // 分類
        cpu: 'CPU 處理器',
        motherboard: '主機板',
        memory: '記憶體',
        graphics_card: '顯示卡',
        storage: '儲存裝置',
        power_supply: '電源供應器',
        case: '機殼',
        cooler: '散熱器',
        other: '其他',
        
        // 搜索和篩選
        search_receipts: '搜索收據...',
        search_items: '搜索項目...',
        all_payment_methods: '所有付款方式',
        all_categories: '所有分類',
        
        // 公司信息
        company_name: 'KelvinKMS',
        company_website: 'KelvinKMS.com',
        company_phone: '************',
        company_email: '<EMAIL>',
        
        // 其他錯誤消息
        error_no_items_to_print: '請先添加項目到收據中',
        error_no_items_to_save: '請先添加項目到收據中',
        error_no_items_to_generate: '請先添加項目到收據中',
        confirm_clear_form: '確定要清空表單嗎？',
        form_cleared: '表單已清空',
        preset_feature_unavailable: '預設項目功能暫時無法使用',
        
        // 系統消息
        system_init_credits: '系統初始化贈送點數',
        admin_add_credits: '管理員增加點數',
        admin_deduct_credits: '管理員扣除點數',
        save_receipt_cost: '保存收據費用',
        print_receipt_cost: '列印收據費用',
        system_operation: '系統操作',
        no_matching_items: '沒有找到匹配的項目',
        no_saved_configurations: '尚未保存任何配置',
        welcome_back: '歡迎回來',
        config_save_error: '保存配置時發生錯誤',

        // 配置管理
        manage_configurations: '管理配置',
        add_configuration: '新增配置',
        save_configuration: '保存配置',
        save_configuration_title: '保存配置',
        config_name: '配置名稱',
        config_description: '配置描述',
        config_name_placeholder: '請輸入配置名稱',
        config_description_placeholder: '請輸入配置描述（可選）',
        config_name_required: '請輸入配置名稱',
        save_failed: '保存失敗',
        all_categories: '所有分類',
        receipt_configs: '收據配置',
        customer_configs: '客戶配置',
        system_configs: '系統配置',
        search_configs: '搜索配置...',

        // 郵件驗證
        verification_success_title: '郵箱驗證成功！',
        verification_success_message: '您的帳號已成功激活，您已獲得10個免費點數。現在可以登入了。',
        verification_failed_title: '驗證失敗',
        verification_failed_message: '驗證鏈接無效或已過期。鏈接可能已被使用或已過期。',
        login_now: '立即登入',
        go_home: '返回首頁',
        register_again: '重新註冊',
        try_login: '嘗試登入',

        // 代理登入
        proxy_session: '代理會話',
        return_to_admin: '返回管理員',
        login_as_user: '登入為用戶',
        proxy_login_confirm: '確定要登入為此用戶嗎？',
        proxy_login_success: '成功登入為用戶',
        proxy_login_failed: '代理登入失敗',
        return_admin_confirm: '確定要返回管理員帳號嗎？',
        return_admin_success: '成功返回管理員帳號',
        return_admin_failed: '返回管理員失敗',
        proxy_session_warning: '代理會話即將過期',
        session_expired: '會話已過期',

        // 快速導覽
        add_item: '添加項目',
        receipt_items: '收據項目',
        receipt_preview: '收據預覽',
        
        // 密碼驗證相關
        password_requirements: '密碼要求',
        password_rule_length: '至少8個字符',
        password_rule_uppercase: '包含一個大寫字母',
        password_rule_number: '包含一個數字',
        password_rule_special: '包含一個特殊符號',
        password_match: '密碼匹配',
        password_no_match: '密碼不匹配',
        password_strength_weak: '密碼強度：弱',
        password_strength_medium: '密碼強度：中等',
        password_strength_strong: '密碼強度：強',
        password: '密碼',
        confirm_password: '確認密碼'
    },
    
    en: {
        // 基本界面
        app_title: 'KMS Receipt Maker',
        nav_create: 'Create Receipt',
        nav_history: 'History',
        language: 'Language',
        
        // 收據創建
        create_receipt: 'Create New Receipt',
        customer_info: 'Customer Info.',
        customer_name: 'Customer Name',
        customer_phone: 'Phone Number',
        customer_email: 'Email Address',
        customer_address: 'Address',
        payment_method: 'Payment Method',
        payment_cash: 'Cash',
        payment_card: 'Credit Card',
        payment_transfer: 'Bank Transfer',
        payment_other: 'Other',
        
        // 項目管理
        receipt_items: 'Receipt Items',
        add_item: 'Add Item',
        item_name: 'Item Name',
        item_description: 'Item Description',
        item_category: 'Category',
        quantity: 'Quantity',
        unit_price: 'Unit Price',
        total_price: 'Total Price',
        remove_item: 'Remove Item',
        select_preset: 'Select Preset',
        confirm_add: 'Confirm Add',
        clear: 'Clear',
        no_items: 'No items added yet',
        receipt_info: 'Receipt Information',
        service: 'Service',
        
        // 總計
        totals: 'Totals',
        subtotal: 'Subtotal',
        discount: 'Discount Amount',
        tax: 'Tax',
        total: 'Total',
        notes: 'Notes',
        
        // 操作按鈕
        generate_receipt: 'Generate Receipt',
        clear_form: 'Clear Form',
        save_receipt: 'Save Receipt',
        print_receipt: 'Print Receipt',
        close: 'Close',
        
        // 預覽和歷史
        receipt_preview: 'Receipt Preview',
        preview_placeholder: 'Receipt preview will be displayed here',
        receipt_history: 'Receipt History',
        receipt_details: 'Receipt Details',
        search_placeholder: 'Search receipts...',
        
        // 收據字段
        receipt_number: 'Receipt Number',
        receipt_date: 'Date Issued',
        
        // 表格標題
        item: 'Item',
        description: 'Description',
        category: 'Category',
        qty: 'Qty',
        price: 'Price',
        amount: 'Amount',
        
        // 狀態
        paid: 'Paid',
        pending: 'Pending',
        cancelled: 'Cancelled',
        thank_you: 'Thank you for your business!',
        seller_signature: 'Seller Signature',
        buyer_signature: 'Buyer Signature',
        signature_date: 'Signature Date',
        
        // 錯誤和成功消息
        error_customer_name: 'Please enter customer name',
        error_no_items: 'Please add at least one item',
        error_invalid_quantity: 'Please enter a valid quantity',
        error_invalid_price: 'Please enter a valid price',
        success_receipt_saved: 'Receipt saved successfully',
        success_receipt_generated: 'Receipt generated successfully',
        success_item_added: 'Item added successfully',
        success_item_updated: 'Item updated successfully',
        success_item_removed: 'Item removed',
        edit_item: 'Edit Item',
        save_changes: 'Save Changes',
        enter_config_name: 'Please enter configuration name',
        config_saved_success: 'Configuration saved successfully!',
        config_loaded_success: 'Configuration loaded successfully!',
        config_deleted_success: 'Configuration deleted successfully!',
        config_not_found: 'Configuration not found',
        confirm_delete_config: 'Are you sure you want to delete this configuration?',
        save_configuration: 'Save Configuration',
        load_configuration: 'Load Configuration',
        manage_configurations: 'Manage Configurations',
        add_preset: 'Add Preset',
        edit_preset: 'Edit Preset',
        delete_preset: 'Delete Preset',
        save: 'Save',
        cancel: 'Cancel',
        move_up: 'Move Up',
        move_down: 'Move Down',
        confirm_delete_preset: 'Are you sure you want to delete this preset item?',
        preset_saved_success: 'Preset item saved successfully!',
        preset_deleted_success: 'Preset item deleted successfully!',
        original_price: 'Original Price',
        special_price: 'Special Price',
        discount_percentage: 'Discount Percentage',
        hide_price: 'Hide Price (Show N/A on receipt)',
        update: 'Update',
        add_item_description: 'Click the button below to add items to the receipt',
        upload_logo: 'Upload Logo',
        logo_upload_hint: 'Supports JPG, PNG, GIF formats, no file size limit',
        error_invalid_image: 'Please select a valid image file',
        logo_uploaded_success: 'Logo uploaded successfully!',
        logo_preview: 'Logo Preview',
        logo_info: 'Image Information',
        file_name: 'File Name',
        file_type: 'File Type',
        file_size: 'File Size',
        image_dimensions: 'Image Dimensions',
        remove_logo: 'Remove Logo',
        logo_removed: 'Logo removed',
        tax_rate: 'Tax Rate',
        clear_all: 'Clear All',
        confirm_clear_all: 'Are you sure you want to clear all items?',
        all_items_cleared: 'All items cleared',
        
        // 點數系統
        generating_pdf: 'Generating PDF, please wait...',
        pdf_generated_success: 'PDF generated and downloaded successfully',
        pdf_generation_failed: 'PDF generation failed',
        credits_deducted: 'Credits deducted',
        remaining_credits: 'Remaining credits',
        credits_warning: 'Warning: Your credits are below 10, please contact administrator for recharge',
        print_authorization_success: 'Print authorization successful!',
        print_authorization_failed: 'Print authorization failed, please try again later',
        saving: 'Saving...',
        save_failed: 'Save failed',
        config_save_failed: 'Configuration save failed',
        receipt_saved_pdf_failed: 'Receipt saved, but PDF generation failed',
        config_load_failed: 'Failed to load configuration',
        config_delete_failed: 'Failed to delete configuration',
        print_failed: 'Print failed',
        delete_failed: 'Delete failed',
        customer_info_updated_success: 'Customer information updated successfully',
        customer_info_update_failed: 'Error updating customer information',
        credits_deducted_balance: 'Deducted {deducted} credits, balance: {balance}',
        subtotal_label: 'Subtotal',
        discount_label: 'Discount',
        tax_label: 'Tax',
        total_label: 'Total',
        save_receipt_button: 'Save Receipt',
        receipt_saved_pdf_success: 'Receipt saved and PDF generated successfully',
        save_error: 'Error occurred while saving',
        processing: 'Processing...',
        clear_form_title: 'Clear Form',
        operation_success: 'Operation Successful',
        generate_receipt_first: 'Please generate receipt first',
        notice: 'Notice',
        confirm_approve_user: 'Are you sure you want to approve this user?',
        confirm_reject_user: 'Are you sure you want to reject this user?',
        confirm_delete_user: 'Are you sure you want to delete this user? This action cannot be undone!',
        select_users_to_approve: 'Please select users to approve',
        select_users_to_reject: 'Please select users to reject',
        confirm_clear_form: 'Are you sure you want to clear the form? This will remove all entered data.',
        form_cleared: 'Form has been cleared',
        // Profile page translations
        profile_settings: 'Profile Settings',
        back_to_home: 'Back to Home',
        logout: 'Logout',
        delete_account: 'Delete Account',
        delete_account_title: 'Delete Account',
        delete_account_subtitle: 'This action cannot be undone',
        warning_title: 'Warning',
        warning_permanent: 'This action is permanent and cannot be undone',
        warning_receipts: 'All your receipts will be permanently deleted',
        warning_presets: 'All your presets and configurations will be lost',
        warning_credits: 'All remaining credits will be forfeited',
        warning_access: 'You will lose access to your account immediately',
        your_data_title: 'Your Data Summary',
        receipts: 'Receipts',
        presets: 'Presets',
        configurations: 'Configurations',
        transactions: 'Transactions',
        confirmation_required: 'Confirmation Required',
        enter_password: 'Enter your password to confirm:',
        type_delete: 'Type "DELETE" to confirm:',
        cancel: 'Cancel',
        registration_date: 'Registration Date',
        last_login: 'Last Login',
        never_logged_in: 'Never logged in',
        profile_information: 'Profile Information',
        members_readonly_notice: 'Members can only change their password. Please contact an administrator to change other information.',
        username: 'Username',
        email_address: 'Email Address',
        first_name: 'First Name',
        last_name: 'Last Name',
        phone_number: 'Phone Number',
        update_profile: 'Update Profile',
        change_password: 'Change Password',
        current_password: 'Current Password',
        please_enter_password: 'Please enter password',
        new_password: 'New Password',
        password_min_length: 'Password must be at least 6 characters',
        confirm_new_password: 'Confirm New Password',
        // Error messages
        please_enter_current_password: 'Please enter current password',
        current_password_incorrect: 'Current password is incorrect',
        please_enter_new_password: 'Please enter new password',
        new_password_min_length: 'New password must be at least 6 characters',
        passwords_do_not_match: 'New passwords do not match',
        password_updated_successfully: 'Password updated successfully!',
        failed_to_update_password: 'Failed to update password, please try again',
        system_error_try_later: 'System error, please try again later',
        admin: 'Admin',
        member: 'Member',
        credits_history: 'Credits History',
        current_credits: 'Current Credits',
        total_earned: 'Total Earned',
        total_spent: 'Total Spent',
        all_types: 'All Types',
        credits_added: 'Credits Added',
        credits_deducted: 'Credits Deducted',
        all_time: 'All Time',
        today: 'Today',
        this_week: 'This Week',
        this_month: 'This Month',
        loading: 'Loading...',
        no_records: 'No Records',
        no_records_desc: 'You have no credit transaction records yet',
        
        // 分類
        cpu: 'CPU Processor',
        motherboard: 'Motherboard',
        memory: 'Memory',
        graphics_card: 'Graphics Card',
        storage: 'Storage',
        power_supply: 'Power Supply',
        case: 'Case',
        cooler: 'Cooler',
        other: 'Other',
        
        // 搜索和篩選
        search_receipts: 'Search receipts...',
        search_items: 'Search items...',
        all_payment_methods: 'All payment methods',
        all_categories: 'All categories',
        
        // 公司信息
        company_name: 'KelvinKMS',
        company_website: 'KelvinKMS.com',
        company_phone: '************',
        company_email: '<EMAIL>',
        
        // 其他錯誤消息
        error_no_items_to_print: 'Please add items to the receipt first',
        error_no_items_to_save: 'Please add items to the receipt first',
        error_no_items_to_generate: 'Please add items to the receipt first',
        confirm_clear_form: 'Are you sure you want to clear the form?',
        form_cleared: 'Form cleared',
        preset_feature_unavailable: 'Preset items feature is temporarily unavailable',
        
        // 系統消息
        system_init_credits: 'System Initialization Bonus Credits',
        admin_add_credits: 'Admin Added Credits',
        admin_deduct_credits: 'Admin Deducted Credits',
        save_receipt_cost: 'Save Receipt Cost',
        print_receipt_cost: 'Print Receipt Cost',
        system_operation: 'System Operation',
        no_matching_items: 'No matching items found',
        no_saved_configurations: 'No configurations saved yet',
        welcome_back: 'Welcome back',
        config_save_error: 'Error saving configuration',

        // Configuration management
        manage_configurations: 'Manage Configurations',
        add_configuration: 'Add Configuration',
        save_configuration: 'Save Configuration',
        save_configuration_title: 'Save Configuration',
        config_name: 'Configuration Name',
        config_description: 'Configuration Description',
        config_name_placeholder: 'Enter configuration name',
        config_description_placeholder: 'Enter description (optional)',
        config_name_required: 'Please enter configuration name',
        save_failed: 'Save failed',
        all_categories: 'All Categories',
        receipt_configs: 'Receipt Configurations',
        customer_configs: 'Customer Configurations',
        system_configs: 'System Configurations',
        search_configs: 'Search configurations...',

        // Email verification
        verification_success_title: 'Email Verified Successfully!',
        verification_success_message: 'Your account has been activated and you have received 10 free credits. You can now log in.',
        verification_failed_title: 'Verification Failed',
        verification_failed_message: 'Invalid or expired verification link. The link may have already been used or has expired.',
        login_now: 'Login Now',
        go_home: 'Go to Home',
        register_again: 'Register Again',
        try_login: 'Try Login',

        // Proxy login
        proxy_session: 'Proxy Session',
        return_to_admin: 'Return to Admin',
        login_as_user: 'Login as User',
        proxy_login_confirm: 'Are you sure you want to login as this user?',
        proxy_login_success: 'Successfully logged in as user',
        proxy_login_failed: 'Proxy login failed',
        return_admin_confirm: 'Are you sure you want to return to admin account?',
        return_admin_success: 'Successfully returned to admin account',
        return_admin_failed: 'Failed to return to admin',
        proxy_session_warning: 'Proxy session will expire soon',
        session_expired: 'Session expired',

        // Quick navigation
        add_item: 'Add Item',
        receipt_items: 'Receipt Items',
        receipt_preview: 'Receipt Preview',
        
        // Password validation related
        password_requirements: 'Password Requirements',
        password_rule_length: 'At least 8 characters',
        password_rule_uppercase: 'Contains one uppercase letter',
        password_rule_number: 'Contains one number',
        password_rule_special: 'Contains one special character',
        password_match: 'Passwords match',
        password_no_match: 'Passwords do not match',
        password_strength_weak: 'Password strength: Weak',
        password_strength_medium: 'Password strength: Medium',
        password_strength_strong: 'Password strength: Strong',
        password: 'Password',
        confirm_password: 'Confirm Password',

        // Trial system
        trial_period: 'Trial Period',
        trial_expires_in: 'Trial expires in',
        trial_expired: 'Trial Expired',
        trial_days_remaining: 'days remaining',
        trial_upgrade_required: 'Upgrade Required',
        trial_feature_restricted: 'This feature is not available during trial period',
        trial_contact_admin: 'Please contact administrator to upgrade to paid membership',
        trial_save_restricted: 'Save Receipt feature is disabled during trial',
        trial_print_restricted: 'Print Receipt feature is disabled during trial',
        trial_upgrade_message: 'Upgrade to paid membership to unlock all features'
    }
};

// 全局變量
let currentLanguage = localStorage.getItem('language') || 'en';
let systemConfig = {};
let isChangingLanguage = false; // 防止遞歸調用的標誌

/**
 * 載入系統配置
 */
async function loadSystemConfig() {
    try {
        const response = await fetch('php/get_system_config.php');
        if (response.ok) {
            const config = await response.json();
            if (config.success) {
                systemConfig = config.data;
            }
        }
    } catch (error) {
        console.log('無法載入系統配置，使用默認設置');
    }
}

/**
 * 初始化語言系統
 */
async function initializeLanguage() {
    await loadSystemConfig();
    
    // 確保currentLanguage有值
    if (!currentLanguage) {
        currentLanguage = 'en';
        localStorage.setItem('language', currentLanguage);
    }
    
    // 應用當前語言
    applyLanguage(currentLanguage);
    
    // 更新語言選擇器
    updateLanguageSelector();
}

/**
 * 切換語言
 */
function changeLanguage(lang) {
    if (isChangingLanguage) {
        return; // 防止遞歸調用
    }
    
    isChangingLanguage = true;
    
    try {
        currentLanguage = lang;
        localStorage.setItem('language', lang);
        
        // 應用語言
        applyLanguage(lang);
        
        // 更新語言選擇器
        updateLanguageSelector();
        
        // 觸發語言變更事件
        const event = new CustomEvent('languageChanged', { detail: { language: lang } });
        document.dispatchEvent(event);
        
        // 更新模態框語言
        updateModalLanguage(lang);
    } finally {
        isChangingLanguage = false;
    }
}

/**
 * 應用語言到頁面
 */
function applyLanguage(lang, container = document) {
    const langData = languages[lang];
    if (!langData) {
        console.error('不支持的語言:', lang);
        return;
    }

    // 更新容器內所有帶有 data-lang 屬性的元素
    const elements = container.querySelectorAll('[data-lang]');
    elements.forEach(element => {
        const key = element.getAttribute('data-lang');
        if (langData[key]) {
            // 根據元素類型更新內容
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'submit' || element.type === 'button') {
                    element.value = langData[key];
                } else {
                    element.placeholder = langData[key];
                }
            } else if (element.tagName === 'OPTION') {
                element.textContent = langData[key];
            } else {
                element.textContent = langData[key];
            }
        }
    });

    // 只在處理整個文檔時更新頁面標題
    if (container === document) {
        // 更新頁面標題
        if (langData.app_title) {
            document.title = langData.app_title;
        }

        // 更新搜索框佔位符
        const searchInput = document.getElementById('searchInput');
        if (searchInput && langData.search_placeholder) {
            searchInput.placeholder = langData.search_placeholder;
        }
    }
}

/**
 * 更新語言選擇器狀態
 */
function updateLanguageSelector() {
    const langButtons = document.querySelectorAll('.kms-lang-btn');
    langButtons.forEach(btn => {
        btn.classList.remove('active');
        const langCode = btn.getAttribute('data-lang-code');
        if (langCode === currentLanguage) {
            btn.classList.add('active');
        }
    });
}

/**
 * 獲取文本
 */
function getText(key) {
    return languages[currentLanguage][key] || key;
}

/**
 * 獲取當前語言
 */
function getCurrentLanguage() {
    return currentLanguage;
}

/**
 * 獲取系統配置
 */
function getSystemConfig(key) {
    return systemConfig[key];
}

/**
 * 重新應用語言到容器
 */
function reapplyLanguage(container = document) {
    applyLanguage(currentLanguage, container);
}

/**
 * 應用語言到指定容器的元素
 */
function applyLanguageToContainer(lang, container = document) {
    applyLanguage(lang, container);
}

/**
 * 更新模態框語言內容
 */
function updateModalLanguage(lang) {
    // 更新預設項目模態框
    const presetModal = document.getElementById('presetModal');
    if (presetModal && presetModal.classList.contains('is-open')) {
        // 更新搜索輸入框佔位符
        const searchInput = document.getElementById('presetSearch');
        if (searchInput) {
            if (lang === 'en') {
                searchInput.placeholder = 'Search items...';
            } else {
                searchInput.placeholder = '搜索項目...';
            }
        }

        // 更新分類篩選器的第一個選項
        const categoryFilter = document.getElementById('presetCategoryFilter');
        if (categoryFilter && categoryFilter.options.length > 0) {
            if (lang === 'en') {
                categoryFilter.options[0].textContent = 'All categories';
            } else {
                categoryFilter.options[0].textContent = '所有分類';
            }
        }
    }
}

/**
 * 格式化數字為貨幣格式
 */
function formatCurrency(amount) {
    // 處理 null, undefined, 或無效值
    if (amount === null || amount === undefined || amount === '' || isNaN(amount)) {
        return '$0.00';
    }

    // 獲取貨幣配置，處理字符串和對象兩種情況
    let config = getSystemConfig('currency') || 'USD';

    // 如果 config 是字符串，轉換為對象格式
    if (typeof config === 'string') {
        // 根據貨幣代碼設置符號
        const currencySymbols = {
            'USD': '$',
            'HKD': 'HK$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥'
        };
        config = {
            symbol: currencySymbols[config] || '$',
            position: 'before'
        };
    }

    // 確保 config 有默認值
    config = { symbol: '$', position: 'before', ...config };

    const numericAmount = parseFloat(amount);

    // 再次檢查轉換後的數值是否有效
    if (isNaN(numericAmount)) {
        return config.symbol + '0.00';
    }

    const formatted = numericAmount.toFixed(2);

    if (config.position === 'after') {
        return formatted + config.symbol;
    } else {
        return config.symbol + formatted;
    }
}

/**
 * 格式化日期
 */
function formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

// 初始化
document.addEventListener('DOMContentLoaded', initializeLanguage);

// 全局函數
window.changeLanguage = changeLanguage;

// 語言管理器對象
window.LanguageManager = {
    changeLanguage,
    getText,
    getCurrentLanguage,
    getSystemConfig,
    formatCurrency,
    formatDate,
    reapplyLanguage,
    applyLanguageToContainer,
    updateModalLanguage
};
