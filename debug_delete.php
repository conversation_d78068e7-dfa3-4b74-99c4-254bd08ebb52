<?php
// Simple test to verify delete functionality works with session
session_start();

echo "=== Testing Delete Functionality ===\n";

// Set up a test session (simulate logged in user)
$_SESSION['user_id'] = 1;
echo "Session user_id set to: " . $_SESSION['user_id'] . "\n";

// Test with a simple cURL request to the actual endpoint
$url = 'http://localhost:8000/php/delete_pc_part.php';
$data = json_encode(['id' => 1]); // Test with ID 1

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($data)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'test_cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'test_cookies.txt');

// First establish a session by visiting the main page
echo "Establishing session...\n";
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/index.php');
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_POSTFIELDS, null);
$sessionResponse = curl_exec($ch);
$sessionCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
echo "Session establishment status: $sessionCode\n";

// Now try the delete request
echo "Attempting delete request...\n";
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($data)
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Delete response status: $httpCode\n";
echo "Delete response body: $response\n";

if ($httpCode == 200) {
    echo "SUCCESS: Delete request returned 200 OK\n";
} else {
    echo "ISSUE: Delete request returned status $httpCode\n";
}

curl_close($ch);

// Clean up
if (file_exists('test_cookies.txt')) {
    unlink('test_cookies.txt');
}

echo "\n=== Test Complete ===\n";
?>