<?php
/**
 * 收據管理類
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

class ReceiptManager {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 生成收據編號
     */
    public function generateReceiptNumber() {
        // 使用固定格式 KMS-UltraVIP-0000001
        // 從資料庫獲取最大收據編號以確定下一個序號
        $pattern = 'KMS-UltraVIP-';

        $sql = "SELECT receipt_number FROM receipts
                WHERE receipt_number LIKE ?
                ORDER BY receipt_number DESC
                LIMIT 1";

        $result = $this->db->fetch($sql, [$pattern . '%']);

        $maxNumber = 0;
        if ($result && !empty($result['receipt_number'])) {
            $number = (int)substr($result['receipt_number'], strlen($pattern));
            $maxNumber = $number;
        }

        $nextNumber = str_pad($maxNumber + 1, 7, '0', STR_PAD_LEFT);
        return $pattern . $nextNumber;
    }
    
    /**
     * 保存收據
     */
    public function saveReceipt($receiptData, $items, $userId = null) {
        try {
            $this->db->beginTransaction();
            
            // 生成收據編號
            $receiptNumber = $this->generateReceiptNumber();
            
            // 插入收據主記錄（添加用戶ID和新的小計計算欄位）
            $sql = "INSERT INTO receipts (
                receipt_number, user_id, customer_name, customer_phone, customer_email,
                customer_address, subtotal, tax_amount, discount_amount,
                total_amount, original_total, item_savings, total_savings, savings_percent,
                payment_method, notes, receipt_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $receiptId = $this->db->insert($sql, [
                $receiptNumber,
                $userId, // 添加用戶ID
                $receiptData['customer_name'],
                $receiptData['customer_phone'] ?? null,
                $receiptData['customer_email'] ?? null,
                $receiptData['customer_address'] ?? null,
                $receiptData['subtotal'],
                $receiptData['tax_amount'],
                $receiptData['discount_amount'] ?? 0,
                $receiptData['total_amount'],
                $receiptData['original_total'] ?? 0,
                $receiptData['item_savings'] ?? 0,
                $receiptData['total_savings'] ?? 0,
                $receiptData['savings_percent'] ?? 0,
                $receiptData['payment_method'] ?? 'Cash',
                $receiptData['notes'] ?? null,
                date('Y-m-d') // Add current date for receipt_date
            ]);
            
            // 插入收據項目
            $itemSql = "INSERT INTO receipt_items (
                receipt_id, item_name, description, category,
                quantity, unit_price, line_total, original_price,
                special_price, discount_percent, hide_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            foreach ($items as $item) {
                // Ensure hide_price is properly converted to integer (0 or 1)
                $hidePriceValue = $item['hide_price'] ?? false;
                $hidePrice = ($hidePriceValue === true || $hidePriceValue === 'true' || $hidePriceValue === 1 || $hidePriceValue === '1') ? 1 : 0;

                $this->db->insert($itemSql, [
                    $receiptId,
                    $item['name'],
                    $item['description'] ?? null,
                    $item['category'] ?? null,
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price'],
                    $item['original_price'] ?? $item['unit_price'], // 如果沒有原價，使用單價
                    $item['special_price'] ?? null,
                    $item['discount_percent'] ?? 0,
                    $hidePrice // Use the properly converted integer value
                ]);
            }
            
            $this->db->commit();
            
            return [
                'receipt_id' => $receiptId,
                'receipt_number' => $receiptNumber
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log('ReceiptManager saveReceipt error: ' . $e->getMessage());
            error_log('Receipt data: ' . print_r($receiptData, true));
            error_log('Items data: ' . print_r($items, true));
            throw $e;
        }
    }
    
    /**
     * 獲取收據列表
     */
    public function getReceipts($page = 1, $limit = 20, $filters = [], $userId = null) {
        // 構建WHERE條件
        $whereConditions = [];
        $params = [];

        // 用戶隔離：只顯示當前用戶的收據（管理員可以看到所有）
        if ($userId !== null && !$this->isAdmin($userId)) {
            $whereConditions[] = "user_id = ?";
            $params[] = $userId;
        }

        // 搜索過濾
        if (!empty($filters['search'])) {
            $whereConditions[] = "(receipt_number LIKE ? OR customer_name LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // 付款方式過濾
        if (!empty($filters['payment_method'])) {
            $whereConditions[] = "payment_method = ?";
            $params[] = $filters['payment_method'];
        }

        // 日期範圍過濾
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        // 獲取總數
        $countSql = "SELECT COUNT(*) as total FROM receipts {$whereClause}";
        $totalResult = $this->db->fetch($countSql, $params);
        $total = $totalResult['total'];

        // 獲取分頁數據
        $offset = ($page - 1) * $limit;
        $sql = "SELECT * FROM receipts {$whereClause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?";

        $params[] = $limit;
        $params[] = $offset;

        $receipts = $this->db->fetchAll($sql, $params);

        return [
            'receipts' => $receipts,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($total / $limit)
        ];
    }

    /**
     * 獲取單個收據詳情
     */
    public function getReceiptById($id, $userId = null) {
        // 構建SQL查詢，添加用戶隔離
        $sql = "SELECT * FROM receipts WHERE id = ?";
        $params = [$id];
        
        // 用戶隔離：只允許查看自己的收據（管理員可以查看所有）
        if ($userId !== null && !$this->isAdmin($userId)) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }
        
        $receipt = $this->db->fetch($sql, $params);

        if (!$receipt) {
            return null;
        }

        // 獲取收據項目，並將 line_total 映射為 total_price 以保持前端兼容性
        $itemsSql = "SELECT *, line_total as total_price FROM receipt_items WHERE receipt_id = ? ORDER BY id";
        $items = $this->db->fetchAll($itemsSql, [$id]);

        // 確保數值字段正確轉換
        foreach ($items as &$item) {
            $item['quantity'] = floatval($item['quantity']);
            $item['unit_price'] = floatval($item['unit_price']);
            $item['total_price'] = floatval($item['total_price']);
            $item['line_total'] = floatval($item['line_total']); // 保留原字段
            $item['original_price'] = floatval($item['original_price'] ?? 0);
            $item['special_price'] = floatval($item['special_price'] ?? 0);
            $item['discount_percent'] = intval($item['discount_percent'] ?? 0);
            $item['hide_price'] = intval($item['hide_price'] ?? 0);

            // Debug logging
            error_log("Item loaded: {$item['item_name']}, unit_price: {$item['unit_price']}, total_price: {$item['total_price']}, line_total: {$item['line_total']}");
        }

        $receipt['items'] = $items;

        return $receipt;
    }
    
    /**
     * 獲取電腦零件列表
     */
    public function getPcParts($category = '', $userId = null) {
        $whereClause = 'WHERE is_active = ?';
        $params = [1];

        // 添加用戶過濾：管理員可以看到所有零件，普通用戶只能看到自己的零件
        if ($userId !== null) {
            // 檢查用戶類型
            $userType = isset($_SESSION['user_type']) ? $_SESSION['user_type'] : 'user';

            // 只有非管理員用戶才需要過濾 created_by
            if ($userType !== 'admin') {
                $whereClause .= ' AND created_by = ?';
                $params[] = $userId;
            }
        }

        if (!empty($category)) {
            $whereClause .= ' AND category = ?';
            $params[] = $category;
        }

        // 檢查 sort_order 欄位是否存在，避免舊資料庫報 Unknown column 錯誤
        try {
            $colCheck = $this->db->fetch(
                'SELECT COUNT(*) AS cnt FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?',[DB_NAME, 'pc_parts', 'sort_order']
            );
            $hasSortOrder = $colCheck && intval($colCheck['cnt']) > 0;
        } catch (Exception $e) {
            // 若資訊架構查詢失敗，保守起見不使用 sort_order
            $hasSortOrder = false;
        }

        $orderBy = $hasSortOrder ? 'ORDER BY sort_order ASC, category, name' : 'ORDER BY category, name';
        $sql = "SELECT * FROM pc_parts {$whereClause} {$orderBy}";
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 獲取零件分類
     */
    public function getCategories() {
        $sql = "SELECT DISTINCT category FROM pc_parts WHERE is_active = 1 ORDER BY category";
        return $this->db->fetchAll($sql);
    }

    /**
     * 刪除單個收據
     */
    public function deleteReceipt($receiptId) {
        try {
            $this->db->beginTransaction();
            
            // 先刪除收據項目
            $sql = "DELETE FROM receipt_items WHERE receipt_id = ?";
            $this->db->execute($sql, [$receiptId]);
            
            // 再刪除收據主記錄
            $sql = "DELETE FROM receipts WHERE id = ?";
            $result = $this->db->execute($sql, [$receiptId]);
            
            $this->db->commit();
            
            return $result;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 批量刪除收據
     */
    public function deleteReceipts($receiptIds, $userId = null) {
        try {
            $this->db->beginTransaction();
            
            // 用戶隔離：只允許刪除自己的收據（管理員可以刪除所有）
            if ($userId !== null && !$this->isAdmin($userId)) {
                // 驗證所有收據都屬於當前用戶
                $placeholders = str_repeat('?,', count($receiptIds) - 1) . '?';
                $checkSql = "SELECT COUNT(*) as count FROM receipts WHERE id IN ($placeholders) AND user_id = ?";
                $params = array_merge($receiptIds, [$userId]);
                $result = $this->db->fetch($checkSql, $params);
                
                if ($result['count'] != count($receiptIds)) {
                    throw new Exception('無權限刪除這些收據');
                }
            }
            
            // 構建 IN 子句的佔位符
            $placeholders = str_repeat('?,', count($receiptIds) - 1) . '?';
            
            // 先刪除收據項目
            $sql = "DELETE FROM receipt_items WHERE receipt_id IN ($placeholders)";
            $this->db->execute($sql, $receiptIds);
            
            // 再刪除收據主記錄
            $sql = "DELETE FROM receipts WHERE id IN ($placeholders)";
            $result = $this->db->execute($sql, $receiptIds);
            
            $this->db->commit();
            
            return $result;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * 檢查用戶是否為管理員
     */
    private function isAdmin($userId) {
        $sql = "SELECT user_type FROM users WHERE id = ?";
        $user = $this->db->fetch($sql, [$userId]);
        return $user && $user['user_type'] === 'admin';
    }
    
    /**
     * 獲取用戶的收據統計
     */
    public function getUserReceiptStats($userId) {
        $whereClause = '';
        $params = [];
        
        // 用戶隔離：只統計當前用戶的收據（管理員可以看到所有）
        if ($userId !== null && !$this->isAdmin($userId)) {
            $whereClause = 'WHERE user_id = ?';
            $params[] = $userId;
        }
        
        $sql = "SELECT 
                    COUNT(*) as total_receipts,
                    SUM(total_amount) as total_amount,
                    AVG(total_amount) as avg_amount,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_receipts,
                    COUNT(CASE WHEN YEARWEEK(created_at) = YEARWEEK(NOW()) THEN 1 END) as week_receipts,
                    COUNT(CASE WHEN MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW()) THEN 1 END) as month_receipts
                FROM receipts {$whereClause}";
        
        return $this->db->fetch($sql, $params);
    }
}
?>
