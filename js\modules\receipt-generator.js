/**
 * Receipt Generator
 * Handles receipt generation, HTML creation, and preview functionality
 */

class ReceiptGenerator {
    constructor() {
        this.defaultPaymentMethod = 'cash';
    }

    /**
     * Generate Receipt
     */
    generateReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_generate') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Get customer info without auto-generation - keep empty if not provided
        const customerName = document.getElementById('customerName')?.value.trim() || '';
        const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
        const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

        // Collect receipt data
        const receiptData = {
            customer: {
                name: customerName, // Keep empty if not provided
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod, // Use default since form field was removed
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                hidePrice: item.hidePrice || false
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            // Removed receiptDate - no longer needed
        };

        // Generate receipt HTML and display in preview area
        const receiptHtml = this.generateReceiptHtml(receiptData);
        this.displayReceiptPreview(receiptHtml);

        // Show action buttons
        const previewActions = document.getElementById('previewActions');
        if (previewActions) {
            previewActions.classList.remove('d-none');
        }

        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Receipt generated successfully!', 'success');
        }
    }

    /**
     * Generate Receipt HTML
     */
    generateReceiptHtml(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        return `
            <div class="receipt-container">
                <div class="receipt-inner">
                    ${window.currentLogo && window.currentLogo.src ? `
                    <div class="receipt-logo">
                        <img src="${window.currentLogo.src}" alt="Company Logo">
                    </div>
                    ` : ''}

                    <div class="preview-receipt-header">
                        <div class="receipt-title">${this.getCompanyName()}</div>
                        <div class="receipt-company-info">
                            ${this.getCompanyInfo()}
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">
                                Receipt Number: ${data.receiptNumber}
                            </div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">${data.customer.name || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">${data.customer.phone || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value">${data.customer.email || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Address:</span>
                            <span class="customer-field-value">${data.customer.address || ''}</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: ${data.items.length} items)</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center receipt-table-header-number-wide">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Original Price</th>
                                    <th class="text-right">Discount</th>
                                    <th class="text-right">Final Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map((item, index) => {
            const originalPrice = item.originalPrice || item.unitPrice;
            const specialPrice = item.specialPrice || item.unitPrice;
            const discountPercent = item.discountPercent || 0;
            const actualFinalPrice = item.hidePrice ? 0 : specialPrice;

            return `
                                    <tr>
                                        <td class="text-center item-number">${index + 1}</td>
                                        <td class="text-left">
                                            <div class="item-name">${item.name}</div>
                                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                                        </td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(originalPrice)}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : (discountPercent > 0 ? discountPercent + '%' : '-')}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(actualFinalPrice)}</td>
                                        <td class="text-center">${item.quantity}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                    </tr>
                                `;
        }).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals">
                        <div class="totals-inline">
                            ${data.totals.originalTotal && data.totals.originalTotal > data.totals.subtotal ? `
                            <span class="total-item">Original Total: <span class="amount text-muted">${formatCurrency(data.totals.originalTotal)}</span></span>
                            ` : ''}
                            <span class="total-item">Subtotal: <span class="amount">${formatCurrency(data.totals.subtotal)}</span></span>
                            ${data.totals.itemSavings && data.totals.itemSavings > 0 ? `
                            <span class="total-item">Item Savings: <span class="amount text-success">-${formatCurrency(data.totals.itemSavings)}</span></span>
                            ` : ''}
                            ${data.totals.discount && data.totals.discount > 0 ? `
                            <span class="total-item">Discount: <span class="amount text-success">-${formatCurrency(data.totals.discount)}</span></span>
                            ` : ''}
                            ${data.totals.tax && data.totals.tax > 0 ? `
                            <span class="total-item">Tax: <span class="amount">${formatCurrency(data.totals.tax)}</span></span>
                            ` : ''}
                            ${data.totals.totalSavings && data.totals.totalSavings > 0 ? `
                            <span class="total-item">Total Savings: <span class="amount text-success">-${formatCurrency(data.totals.totalSavings)} (${data.totals.savingsPercent ? data.totals.savingsPercent.toFixed(1) : '0.0'}%)</span></span>
                            ` : ''}
                            <span class="total-item final-total">Final Total: <span class="amount">${formatCurrency(data.totals.total)}</span></span>
                        </div>
                    </div>

                    <div class="receipt-bottom-fixed">
                        <div class="payment-method">
                            <div class="payment-options">
                                ${['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'].map(pm => {
                return `
                                    <div class="payment-option-button">
                                        <div class="payment-checkbox"></div>
                                        <span class="payment-label">${pm}</span>
                                    </div>
                                    `;
            }).join('')}
                            </div>
                        </div>

                        ${data.notes ? `
                        <div class="receipt-notes">
                            <h6>Notes</h6>
                            <p>${data.notes}</p>
                        </div>
                        ` : ''}

                        <div class="signature-section">
                            <div class="signature-labels-row">
                                <div class="signature-label-item">
                                    <span class="signature-label">Seller Signature:</span>
                                </div>
                                <div class="signature-label-item">
                                    <span class="signature-label">Buyer Signature:</span>
                                </div>
                                <div class="signature-label-item">
                                    <span class="signature-label">Signature Date:</span>
                                </div>
                            </div>
                            <div class="signature-lines-area">
                                <div class="signature-line-space"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Display Receipt Preview
     */
    displayReceiptPreview(html) {
        const previewElement = document.getElementById('receiptPreview');
        if (previewElement) {
            previewElement.innerHTML = html;
            previewElement.classList.add('has-content');
        }
    }

    /**
     * Update Receipt Preview
     */
    updateReceiptPreview() {
        console.log('updateReceiptPreview called');

        // This function can be called to refresh the preview
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        console.log('Receipt items for preview:', receiptItems);

        if (receiptItems.length > 0) {
            // Get current customer info without auto-generation
            const customerName = document.getElementById('customerName')?.value.trim() || '';
            const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
            const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

            // Collect receipt data without auto-generation
            const receiptData = {
                customer: {
                    name: customerName,
                    phone: customerPhone,
                    email: customerEmail,
                    address: document.getElementById('customerAddress')?.value.trim() || ''
                },
                paymentMethod: this.defaultPaymentMethod,
                items: receiptItems.map(item => ({
                    name: item.name,
                    category: item.category,
                    description: item.description,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    totalPrice: item.totalPrice,
                    originalPrice: item.originalPrice || 0,
                    specialPrice: item.specialPrice || item.unitPrice,
                    discountPercent: item.discountPercent || 0,
                    hidePrice: item.hidePrice || false
                })),
                totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
                notes: document.getElementById('notes')?.value.trim() || '',
                receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            };

            console.log('Receipt data for preview:', receiptData);

            // Generate receipt HTML and display in preview area
            const receiptHtml = this.generateReceiptHtml(receiptData);
            this.displayReceiptPreview(receiptHtml);

            console.log('Receipt preview updated successfully');
        } else {
            // Clear preview if no items
            const previewElement = document.getElementById('receiptPreview');
            if (previewElement) {
                previewElement.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here with beautiful certificate border</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Calculate Totals (fallback method)
     */
    calculateTotals(items) {
        const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
        
        // 獲取折扣設定
        const discountType = document.getElementById('discountType')?.value || 'amount';
        const discountValue = parseFloat(document.getElementById('discountValue')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;
        
        // 計算折扣金額
        let discountAmount = 0;
        if (discountType === 'percentage') {
            // 百分比折扣
            discountAmount = subtotal * (discountValue / 100);
        } else {
            // 固定金額折扣
            discountAmount = discountValue;
        }
        
        // 確保折扣不超過小計
        discountAmount = Math.min(discountAmount, subtotal);
        
        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            discountType: discountType,
            discountValue: discountValue,
            tax: taxAmount,
            taxRate: taxRate,
            total: total
        };
    }

    /**
     * Generate Customer Name
     */
    generateCustomerName() {
        const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'James', 'Jessica'];
        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];

        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

        return `${firstName} ${lastName}`;
    }

    /**
     * Generate Customer Phone
     */
    generateCustomerPhone() {
        const areaCode = Math.floor(Math.random() * 900) + 100;
        const exchange = Math.floor(Math.random() * 900) + 100;
        const number = Math.floor(Math.random() * 9000) + 1000;

        return `(${areaCode}) ${exchange}-${number}`;
    }

    /**
     * Generate Customer Email
     */
    generateCustomerEmail() {
        const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        const domain = domains[Math.floor(Math.random() * domains.length)];
        const username = 'customer' + Math.floor(Math.random() * 10000);

        return `${username}@${domain}`;
    }

    /**
     * Get Company Name from member's company info
     */
    getCompanyName() {
        if (window.companyInfoManager) {
            const companyInfo = window.companyInfoManager.getCompanyInfoForReceipt();
            if (companyInfo.name && companyInfo.name.trim()) {
                return companyInfo.name;
            }
        }
        
        // 如果沒有會員公司資訊，使用預設值
        return window.LanguageManager ? 
            window.LanguageManager.getText('company_name') : 
            'KelvinKMS';
    }

    /**
     * Get Company Info from member's company info
     */
    getCompanyInfo() {
        if (window.companyInfoManager) {
            const companyInfo = window.companyInfoManager.getCompanyInfoForReceipt();
            
            // 檢查是否有任何公司資訊
            const hasCompanyInfo = companyInfo.name || companyInfo.website || 
                                 companyInfo.phone || companyInfo.email;
            
            if (hasCompanyInfo) {
                let infoLines = [];
                
                if (companyInfo.website && companyInfo.website.trim()) {
                    infoLines.push(companyInfo.website);
                }
                
                if (companyInfo.phone && companyInfo.phone.trim()) {
                    infoLines.push(companyInfo.phone);
                }
                
                if (companyInfo.email && companyInfo.email.trim()) {
                    infoLines.push(companyInfo.email);
                }
                
                return infoLines.join('<br>');
            }
        }
        
        // 如果沒有會員公司資訊，使用預設值
        const defaultWebsite = window.LanguageManager ? 
            window.LanguageManager.getText('company_website') : 
            'KelvinKMS.com';
        const defaultPhone = window.LanguageManager ? 
            window.LanguageManager.getText('company_phone') : 
            '************';
        const defaultEmail = window.LanguageManager ? 
            window.LanguageManager.getText('company_email') : 
            '<EMAIL>';
            
        return `${defaultWebsite}<br>${defaultPhone}<br>${defaultEmail}`;
    }

    /**
     * Generate Receipt HTML for Print with Pagination
     * Delegated to PrintManager module
     */
    generateReceiptHtmlForPrint(data) {
        if (window.PrintManager) {
            return window.PrintManager.generateReceiptHtmlForPrint(data);
        } else {
            console.error('PrintManager module not available');
            return '<div>Print functionality not available</div>';
        }
    }

    /**
     * Print Receipt
     * Delegated to PrintManager module
     */
    printReceipt() {
        // Preferred path: delegate to PrintManager
        if (window.PrintManager && typeof window.PrintManager.printReceipt === 'function') {
            return window.PrintManager.printReceipt();
        }

        // Attempt lazy initialization in case PrintManager class is loaded but instance missing
        if (!window.PrintManager && typeof window.PrintManager === 'undefined' && typeof window.PrintManager !== 'object' && typeof PrintManager === 'function') {
            try {
                window.PrintManager = new PrintManager();
            } catch (e) {
                console.warn('Lazy init of PrintManager failed:', e);
            }
        }

        if (window.PrintManager && typeof window.PrintManager.printReceipt === 'function') {
            return window.PrintManager.printReceipt();
        }

        // Fallback path: print using ReceiptGenerator's own HTML (no advanced pagination)
        console.warn('PrintManager not available, using ReceiptGenerator fallback printing.');

        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        if (!Array.isArray(receiptItems) || receiptItems.length === 0) {
            const message = window.LanguageManager ? window.LanguageManager.getText('error_no_items_to_print') : 'Please add items to the receipt first';
            if (window.UIManager && typeof window.UIManager.showMessage === 'function') {
                window.UIManager.showMessage(message, 'warning');
            } else {
                alert(message);
            }
            return;
        }

        const customerName = document.getElementById('customerName')?.value.trim() || '';
        const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
        const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';
        const customerAddress = document.getElementById('customerAddress')?.value.trim() || '';

        const totals = window.ItemManager && typeof window.ItemManager.calculateTotals === 'function'
            ? window.ItemManager.calculateTotals()
            : (function(items){
                let subtotal = 0;
                items.forEach(i => subtotal += i.totalPrice || 0);
                const discountType = document.getElementById('discountType')?.value || 'amount';
                const discountValue = parseFloat(document.getElementById('discountValue')?.value) || 0;
                let discountAmount = 0;
                if (discountType === 'percentage') {
                    discountAmount = subtotal * (discountValue / 100);
                } else {
                    discountAmount = discountValue;
                }
                discountAmount = Math.min(discountAmount, subtotal);
                const taxRate = parseFloat(document.getElementById('taxRate')?.value) / 100 || 0;
                const taxAmount = Math.max(0, (subtotal - discountAmount) * taxRate);
                const totalAmount = Math.max(0, subtotal - discountAmount + taxAmount);
                return { subtotal, discount: discountAmount, tax: taxAmount, total: totalAmount };
            })(receiptItems);

        const receiptData = {
            customer: { name: customerName, phone: customerPhone, email: customerEmail, address: customerAddress },
            paymentMethod: 'Cash',
            items: receiptItems,
            totals,
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
        };

        const html = this.generateReceiptHtml(receiptData);

        const printWindow = window.open('', '_blank', 'width=800,height=600');
        if (!printWindow) {
            alert('Please allow popups for this site to enable printing.');
            return;
        }

        const printHtml = `<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Receipt - ${receiptData.receiptNumber}</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="css/receipt.css" rel="stylesheet">
<link href="css/receipt-preview.css" rel="stylesheet">
<link href="css/style.css" rel="stylesheet">
<style>@page{size:letter;margin:0.5in;}body{margin:0;padding:0;background:#fff;-webkit-print-color-adjust:exact;print-color-adjust:exact;}</style>
</head><body>${html}
<script>window.onload=function(){setTimeout(function(){window.print();window.close();},800);};</script>
</body></html>`;

        printWindow.document.write(printHtml);
        printWindow.document.close();
    }




    

    /**
     * Save Receipt
     */
    async saveReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_save') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Get customer info without auto-generation - keep empty if not provided
        const customerName = document.getElementById('customerName')?.value.trim() || '';
        const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
        const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                originalPrice: item.originalPrice || item.unitPrice,
                specialPrice: item.specialPrice || item.unitPrice,
                discountPercent: item.discountPercent || 0,
                hidePrice: item.hidePrice || false
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
        };

        try {
            // First save to database
            const response = await fetch('php/save_receipt.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(receiptData)
            });

            const result = await response.json();

            if (result.success) {
                // Generate and download PDF
                this.generatePDF(receiptData, result.data.receipt_number || result.receipt_number);

                // 顯示保存成功和點數扣除信息
                if (typeof UIManager !== 'undefined') {
                    let message = 'Receipt saved successfully and PDF generated!';
                    
                    // 處理點數扣除信息
                    if (result.credits_deducted && result.remaining_credits !== undefined) {
                        message += `\n💰 ${LanguageManager.getText('credits_deducted')}: ${result.credits_deducted}\n💰 ${LanguageManager.getText('remaining_credits')}: ${result.remaining_credits}`;
                        
                        // 更新界面上的點數顯示
                        if (typeof window.updateUserCreditsDisplay === 'function') {
                            window.updateUserCreditsDisplay(result.remaining_credits);
                        }
                        
                        // 如果點數不足10，顯示警告
                        if (result.remaining_credits < 10) {
                            message += '\n⚠️ ' + LanguageManager.getText('credits_warning');
                        }
                    }
                    
                    // 兼容舊版本的點數信息格式
                    if (result.data && result.data.credit_info) {
                        const creditInfo = result.data.credit_info;
                        const creditMessage = LanguageManager.getText('credits_deducted_balance')
                            .replace('{deducted}', creditInfo.deducted)
                            .replace('{balance}', creditInfo.balance_after);
                        message += ` ${creditMessage}`;
                        
                        // 更新界面上的點數顯示
                        if (typeof window.updateUserCreditsDisplay === 'function') {
                            window.updateUserCreditsDisplay(creditInfo.balance_after);
                        }
                    }
                    
                    // 如果有點數警告，顯示警告
                    if (result.data && result.data.credit_warning) {
                        UIManager.showMessage(result.data.credit_warning, 'warning');
                    }
                    
                    UIManager.showMessage(message, 'success');
                }
            } else {
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Failed to save receipt: ' + result.message, 'error');
                }
            }
        } catch (error) {
            console.error('Error saving receipt:', error);
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Error occurred while saving receipt', 'error');
            }
        }
    }

    /**
     * Generate PDF Receipt - Using html2canvas for exact styling
     */
    async generatePDF(receiptData, receiptNumber) {
        if (window.PDFGenerator) {
            return await window.PDFGenerator.generatePDF(receiptData, receiptNumber);
        } else {
            console.error('PDFGenerator module not available');
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('PDF generation functionality not available', 'error');
            }
        }
    }
}

// Create global instance
window.ReceiptGenerator = new ReceiptGenerator();

// Create global instance
window.ReceiptGenerator = new ReceiptGenerator();

// Debug logging
console.log('ReceiptGenerator module loaded');
console.log('window.ReceiptGenerator:', window.ReceiptGenerator);
console.log('ReceiptGenerator methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.ReceiptGenerator)));

// Export functions for backward compatibility with safe checks
window.generateReceipt = () => {
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.generateReceipt === 'function') {
        return window.ReceiptGenerator.generateReceipt();
    } else {
        console.error('ReceiptGenerator not available for generateReceipt');
    }
};

window.generateReceiptHtml = (data) => {
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.generateReceiptHtml === 'function') {
        return window.ReceiptGenerator.generateReceiptHtml(data);
    } else {
        console.error('ReceiptGenerator not available for generateReceiptHtml');
    }
};

window.displayReceiptPreview = (html) => {
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.displayReceiptPreview === 'function') {
        return window.ReceiptGenerator.displayReceiptPreview(html);
    } else {
        console.error('ReceiptGenerator not available for displayReceiptPreview');
    }
};

window.updateReceiptPreview = () => {
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.updateReceiptPreview === 'function') {
        return window.ReceiptGenerator.updateReceiptPreview();
    } else {
        console.error('ReceiptGenerator not available for updateReceiptPreview');
    }
};

window.printReceipt = () => {
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.printReceipt === 'function') {
        return window.ReceiptGenerator.printReceipt();
    } else {
        console.error('ReceiptGenerator not available for printReceipt');
        alert('Print function not available. Please refresh the page.');
    }
};

window.saveReceipt = () => {
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.saveReceipt === 'function') {
        return window.ReceiptGenerator.saveReceipt();
    } else {
        console.error('ReceiptGenerator not available for saveReceipt');
    }
};
